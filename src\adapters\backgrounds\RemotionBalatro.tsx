import React, { useEffect, useRef } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { Renderer, Program, Mesh, Triangle } from 'ogl';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface RemotionBalatroProps extends RemotionAdapterProps {
  spinRotation?: number;
  spinSpeed?: number;
  offset?: [number, number];
  color1?: string;
  color2?: string;
  color3?: string;
  contrast?: number;
  lighting?: number;
  spinAmount?: number;
  pixelFilter?: number;
  spinEase?: number;
  isRotate?: boolean;
  // Remotion特定属性
  animateColors?: boolean;
  animateRotation?: boolean;
  animateContrast?: boolean;
}

const vertexShader = `
  attribute vec2 position;
  varying vec2 vUv;
  void main() {
    vUv = position * 0.5 + 0.5;
    gl_Position = vec4(position, 0.0, 1.0);
  }
`;

const fragmentShader = `
  precision highp float;
  uniform float uTime;
  uniform vec2 uResolution;
  uniform vec2 uMouse;
  uniform float uSpinRotation;
  uniform float uSpinSpeed;
  uniform vec2 uOffset;
  uniform vec3 uColor1;
  uniform vec3 uColor2;
  uniform vec3 uColor3;
  uniform float uContrast;
  uniform float uLighting;
  uniform float uSpinAmount;
  uniform float uPixelFilter;
  uniform float uSpinEase;
  
  varying vec2 vUv;
  
  void main() {
    vec2 uv = vUv;
    vec2 center = vec2(0.5) + uOffset;
    
    // 计算距离和角度
    vec2 delta = uv - center;
    float dist = length(delta);
    float angle = atan(delta.y, delta.x);
    
    // 时间相关的旋转
    float timeRotation = uTime * uSpinSpeed + uSpinRotation;
    angle += timeRotation * uSpinAmount;
    
    // 创建螺旋效果
    float spiral = sin(dist * uPixelFilter + angle * 3.0 + timeRotation) * 0.5 + 0.5;
    
    // 颜色混合
    vec3 color = mix(uColor1, uColor2, spiral);
    color = mix(color, uColor3, sin(dist * 10.0 + uTime) * 0.5 + 0.5);
    
    // 应用对比度和光照
    color = pow(color, vec3(uContrast));
    color *= uLighting;
    
    gl_FragColor = vec4(color, 1.0);
  }
`;

export const RemotionBalatro: React.FC<RemotionBalatroProps> = ({
  spinRotation = -2.0,
  spinSpeed = 7.0,
  offset = [0.0, 0.0],
  color1 = "#DE443B",
  color2 = "#006BB4",
  color3 = "#162325",
  contrast = 3.5,
  lighting = 0.4,
  spinAmount = 0.25,
  pixelFilter = 745.0,
  spinEase = 1.0,
  isRotate = false,
  startFrame = 0,
  duration = 300,
  delay = 0,
  animateColors = false,
  animateRotation = true,
  animateContrast = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const rendererRef = useRef<Renderer | null>(null);
  const programRef = useRef<Program | null>(null);
  const frame = useCurrentFrame();
  const actualStartFrame = startFrame + delay;

  // 颜色转换函数
  const hexToRgb = (hex: string): [number, number, number] => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    return result ? [
      parseInt(result[1], 16) / 255,
      parseInt(result[2], 16) / 255,
      parseInt(result[3], 16) / 255
    ] : [1, 1, 1];
  };

  // 计算动画值
  const animatedValues = React.useMemo(() => {
    if (frame < actualStartFrame) {
      return {
        time: 0,
        colors: [hexToRgb(color1), hexToRgb(color2), hexToRgb(color3)],
        rotation: spinRotation,
        contrastValue: contrast
      };
    }

    const progress = (frame - actualStartFrame) / 60; // 以秒为单位的时间

    // 动画颜色
    let animatedColors = [hexToRgb(color1), hexToRgb(color2), hexToRgb(color3)];
    if (animateColors) {
      const colorShift = Math.sin(progress * 0.5) * 0.3;
      animatedColors = animatedColors.map(color => 
        color.map(c => Math.max(0, Math.min(1, c + colorShift))) as [number, number, number]
      );
    }

    // 动画旋转
    let animatedRotation = spinRotation;
    if (animateRotation) {
      animatedRotation += interpolate(
        frame,
        [actualStartFrame, actualStartFrame + duration],
        [0, Math.PI * 4],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      );
    }

    // 动画对比度
    let animatedContrast = contrast;
    if (animateContrast) {
      animatedContrast = contrast + Math.sin(progress * 2) * 0.5;
    }

    return {
      time: progress,
      colors: animatedColors,
      rotation: animatedRotation,
      contrastValue: animatedContrast
    };
  }, [frame, actualStartFrame, duration, color1, color2, color3, contrast, spinRotation, animateColors, animateRotation, animateContrast]);

  useEffect(() => {
    if (!containerRef.current) return;

    const canvas = document.createElement('canvas');
    containerRef.current.appendChild(canvas);

    const renderer = new Renderer({
      canvas,
      dpr: Math.min(window.devicePixelRatio, 2),
    });
    rendererRef.current = renderer;

    const geometry = new Triangle(renderer.gl);
    
    const program = new Program(renderer.gl, {
      vertex: vertexShader,
      fragment: fragmentShader,
      uniforms: {
        uTime: { value: 0 },
        uResolution: { value: [canvas.width, canvas.height] },
        uMouse: { value: [0.5, 0.5] },
        uSpinRotation: { value: spinRotation },
        uSpinSpeed: { value: spinSpeed },
        uOffset: { value: offset },
        uColor1: { value: hexToRgb(color1) },
        uColor2: { value: hexToRgb(color2) },
        uColor3: { value: hexToRgb(color3) },
        uContrast: { value: contrast },
        uLighting: { value: lighting },
        uSpinAmount: { value: spinAmount },
        uPixelFilter: { value: pixelFilter },
        uSpinEase: { value: spinEase },
      },
    });
    programRef.current = program;

    const mesh = new Mesh(renderer.gl, { geometry, program });

    const resize = () => {
      if (!containerRef.current) return;
      const { clientWidth, clientHeight } = containerRef.current;
      renderer.setSize(clientWidth, clientHeight);
      program.uniforms.uResolution.value = [clientWidth, clientHeight];
    };

    resize();
    window.addEventListener('resize', resize);

    const render = () => {
      renderer.render({ scene: mesh });
    };

    render();

    return () => {
      window.removeEventListener('resize', resize);
      if (containerRef.current && canvas.parentNode === containerRef.current) {
        containerRef.current.removeChild(canvas);
      }
      renderer.gl.getExtension('WEBGL_lose_context')?.loseContext();
    };
  }, []);

  // 更新uniform值
  useEffect(() => {
    if (!programRef.current) return;

    const program = programRef.current;
    program.uniforms.uTime.value = animatedValues.time;
    program.uniforms.uSpinRotation.value = animatedValues.rotation;
    program.uniforms.uColor1.value = animatedValues.colors[0];
    program.uniforms.uColor2.value = animatedValues.colors[1];
    program.uniforms.uColor3.value = animatedValues.colors[2];
    program.uniforms.uContrast.value = animatedValues.contrastValue;

    if (rendererRef.current) {
      rendererRef.current.render({ scene: new Mesh(rendererRef.current.gl, { 
        geometry: new Triangle(rendererRef.current.gl), 
        program 
      }) });
    }
  }, [animatedValues]);

  const opacity = interpolate(
    frame,
    [actualStartFrame, actualStartFrame + 10],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div 
      ref={containerRef}
      className="w-full h-full"
      style={{ 
        opacity,
        background: 'transparent'
      }}
    />
  );
};

export default RemotionBalatro;
