import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface RemotionRotatingTextProps extends RemotionAdapterProps {
  texts: string[];
  rotationInterval?: number;
  staggerDuration?: number;
  staggerFrom?: 'first' | 'last' | 'center' | 'random' | number;
  loop?: boolean;
  splitBy?: string;
  className?: string;
  mainClassName?: string;
  splitLevelClassName?: string;
  elementLevelClassName?: string;
  // Remotion特定属性
  transitionDuration?: number;
  animationType?: 'slide' | 'fade' | 'scale' | 'rotate';
}

export const RemotionRotatingText: React.FC<RemotionRotatingTextProps> = ({
  texts,
  rotationInterval = 120, // 2秒 at 60fps
  staggerDuration = 5,
  staggerFrom = 'first',
  loop = true,
  splitBy = 'characters',
  className = '',
  mainClassName = '',
  splitLevelClassName = '',
  elementLevelClassName = '',
  startFrame = 0,
  duration = 600,
  delay = 0,
  transitionDuration = 30,
  animationType = 'slide',
}) => {
  const frame = useCurrentFrame();
  const actualStartFrame = startFrame + delay;

  // 计算当前文本索引和过渡进度
  const { currentIndex, nextIndex, transitionProgress } = useMemo(() => {
    if (frame < actualStartFrame) {
      return { currentIndex: 0, nextIndex: 0, transitionProgress: 0 };
    }

    const framesSinceStart = frame - actualStartFrame;
    const totalCycleDuration = rotationInterval + transitionDuration;
    
    if (!loop && framesSinceStart >= texts.length * totalCycleDuration) {
      return { 
        currentIndex: texts.length - 1, 
        nextIndex: texts.length - 1, 
        transitionProgress: 0 
      };
    }

    const cycleFrame = loop ? framesSinceStart % (texts.length * totalCycleDuration) : framesSinceStart;
    const currentCycle = Math.floor(cycleFrame / totalCycleDuration);
    const frameInCycle = cycleFrame % totalCycleDuration;

    const currentIdx = currentCycle % texts.length;
    const nextIdx = (currentCycle + 1) % texts.length;

    let progress = 0;
    if (frameInCycle > rotationInterval) {
      progress = (frameInCycle - rotationInterval) / transitionDuration;
    }

    return {
      currentIndex: currentIdx,
      nextIndex: nextIdx,
      transitionProgress: Math.min(1, progress)
    };
  }, [frame, texts.length, rotationInterval, transitionDuration, loop, actualStartFrame]);

  // 分割文本
  const splitText = (text: string) => {
    if (splitBy === 'characters') {
      return text.split('');
    } else if (splitBy === 'words') {
      return text.split(' ');
    }
    return [text];
  };

  // 计算交错延迟
  const getStaggerDelay = (index: number, total: number) => {
    if (staggerFrom === 'first') {
      return index * staggerDuration;
    } else if (staggerFrom === 'last') {
      return (total - 1 - index) * staggerDuration;
    } else if (staggerFrom === 'center') {
      const center = Math.floor(total / 2);
      return Math.abs(index - center) * staggerDuration;
    } else if (staggerFrom === 'random') {
      return Math.random() * staggerDuration * total;
    } else if (typeof staggerFrom === 'number') {
      return Math.abs(index - staggerFrom) * staggerDuration;
    }
    return 0;
  };

  // 渲染单个文本元素
  const renderTextElement = (text: string, isVisible: boolean, progress: number) => {
    const elements = splitText(text);
    
    return (
      <span className={`${splitLevelClassName} inline-block`}>
        {elements.map((element, index) => {
          const staggerDelay = getStaggerDelay(index, elements.length);
          const elementProgress = interpolate(
            progress * transitionDuration + staggerDelay,
            [0, transitionDuration],
            [0, 1],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );

          let transform = '';
          let opacity = 1;

          if (animationType === 'slide') {
            const yOffset = isVisible 
              ? interpolate(elementProgress, [0, 1], [100, 0])
              : interpolate(elementProgress, [0, 1], [0, -100]);
            transform = `translateY(${yOffset}%)`;
            opacity = isVisible 
              ? interpolate(elementProgress, [0, 1], [0, 1])
              : interpolate(elementProgress, [0, 1], [1, 0]);
          } else if (animationType === 'fade') {
            opacity = isVisible 
              ? interpolate(elementProgress, [0, 1], [0, 1])
              : interpolate(elementProgress, [0, 1], [1, 0]);
          } else if (animationType === 'scale') {
            const scale = isVisible 
              ? interpolate(elementProgress, [0, 1], [0.5, 1])
              : interpolate(elementProgress, [0, 1], [1, 0.5]);
            transform = `scale(${scale})`;
            opacity = isVisible 
              ? interpolate(elementProgress, [0, 1], [0, 1])
              : interpolate(elementProgress, [0, 1], [1, 0]);
          } else if (animationType === 'rotate') {
            const rotation = isVisible 
              ? interpolate(elementProgress, [0, 1], [180, 0])
              : interpolate(elementProgress, [0, 1], [0, -180]);
            transform = `rotateX(${rotation}deg)`;
            opacity = isVisible 
              ? interpolate(elementProgress, [0, 1], [0, 1])
              : interpolate(elementProgress, [0, 1], [1, 0]);
          }

          return (
            <span
              key={`${text}-${index}`}
              className={`${elementLevelClassName} inline-block`}
              style={{
                transform,
                opacity,
                transformOrigin: 'center',
              }}
            >
              {element === ' ' ? '\u00A0' : element}
            </span>
          );
        })}
      </span>
    );
  };

  const currentText = texts[currentIndex] || '';
  const nextText = texts[nextIndex] || '';

  return (
    <span 
      className={`${mainClassName} relative inline-block`}
      style={{
        opacity: interpolate(
          frame,
          [actualStartFrame, actualStartFrame + 10],
          [0, 1],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        )
      }}
    >
      <span className={className}>
        {/* 当前文本 */}
        <span 
          className="absolute inset-0"
          style={{
            opacity: transitionProgress > 0 ? 1 - transitionProgress : 1
          }}
        >
          {renderTextElement(currentText, false, transitionProgress)}
        </span>
        
        {/* 下一个文本 */}
        {transitionProgress > 0 && (
          <span 
            className="absolute inset-0"
            style={{
              opacity: transitionProgress
            }}
          >
            {renderTextElement(nextText, true, transitionProgress)}
          </span>
        )}
        
        {/* 占位符，保持布局 */}
        <span className="invisible">
          {currentText}
        </span>
      </span>
    </span>
  );
};

export default RemotionRotatingText;
