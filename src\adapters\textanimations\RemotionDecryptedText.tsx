import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps, useRemotionAdapter } from '../RemotionAdapter';

interface RemotionDecryptedTextProps extends RemotionAdapterProps {
  text: string;
  speed?: number;
  maxIterations?: number;
  sequential?: boolean;
  revealDirection?: 'start' | 'end' | 'center';
  useOriginalCharsOnly?: boolean;
  characters?: string;
  className?: string;
  encryptedClassName?: string;
  parentClassName?: string;
  // Remotion特定属性
  decryptStartFrame?: number;
  decryptDuration?: number;
}

export const RemotionDecryptedText: React.FC<RemotionDecryptedTextProps> = ({
  text,
  speed = 50,
  maxIterations = 10,
  sequential = false,
  revealDirection = 'start',
  useOriginalCharsOnly = false,
  characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!@#$%^&*()_+',
  className = '',
  parentClassName = '',
  encryptedClassName = '',
  startFrame = 0,
  duration = 120,
  delay = 0,
  decryptStartFrame,
  decryptDuration = 60,
}) => {
  const frame = useCurrentFrame();
  const actualDecryptStart = decryptStartFrame ?? startFrame + delay;
  
  // 计算解密进度
  const decryptProgress = interpolate(
    frame,
    [actualDecryptStart, actualDecryptStart + decryptDuration],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // 生成显示文本
  const displayText = useMemo(() => {
    if (frame < actualDecryptStart) {
      // 完全加密状态
      return text.split('').map(() => 
        characters[Math.floor(Math.random() * characters.length)]
      ).join('');
    }

    const textArray = text.split('');
    const totalChars = textArray.length;
    
    // 计算已解密的字符数量
    let revealedCount: number;
    if (sequential) {
      revealedCount = Math.floor(decryptProgress * totalChars);
    } else {
      revealedCount = Math.floor(decryptProgress * totalChars);
    }

    // 根据解密方向确定解密顺序
    const getRevealedIndices = (): Set<number> => {
      const indices = new Set<number>();
      
      if (revealDirection === 'start') {
        for (let i = 0; i < revealedCount; i++) {
          indices.add(i);
        }
      } else if (revealDirection === 'end') {
        for (let i = totalChars - revealedCount; i < totalChars; i++) {
          indices.add(i);
        }
      } else if (revealDirection === 'center') {
        const center = Math.floor(totalChars / 2);
        const halfRevealed = Math.floor(revealedCount / 2);
        for (let i = center - halfRevealed; i <= center + halfRevealed; i++) {
          if (i >= 0 && i < totalChars) {
            indices.add(i);
          }
        }
      }
      
      return indices;
    };

    const revealedIndices = getRevealedIndices();
    
    return textArray.map((char, index) => {
      if (revealedIndices.has(index)) {
        return char;
      } else {
        // 创建随机字符，但在动画过程中保持一定的稳定性
        const seed = Math.floor(frame / 3) + index; // 每3帧变化一次
        const randomIndex = (seed * 9301 + 49297) % 233280; // 简单的伪随机
        return characters[randomIndex % characters.length];
      }
    }).join('');
  }, [frame, text, characters, decryptProgress, sequential, revealDirection, actualDecryptStart]);

  // 计算每个字符的样式
  const renderCharacters = () => {
    const textArray = text.split('');
    const displayArray = displayText.split('');
    
    return displayArray.map((char, index) => {
      const isRevealed = char === textArray[index];
      
      return (
        <span
          key={index}
          className={isRevealed ? className : encryptedClassName}
          style={{
            opacity: frame >= actualDecryptStart ? 1 : 0.8,
            transition: 'none' // 移除CSS过渡，使用Remotion控制
          }}
        >
          {char}
        </span>
      );
    });
  };

  return (
    <span
      className={`inline-block whitespace-pre-wrap ${parentClassName}`}
      style={{
        opacity: interpolate(
          frame,
          [startFrame + delay, startFrame + delay + 10],
          [0, 1],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        )
      }}
    >
      <span className="sr-only">{text}</span>
      <span aria-hidden="true">
        {renderCharacters()}
      </span>
    </span>
  );
};

export default RemotionDecryptedText;
