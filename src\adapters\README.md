# Remotion 适配组件库

这个库将 react-bits 组件适配为适合 Remotion 视频制作的版本，移除了交互依赖，添加了基于时间轴的动画控制。

## 安装和使用

```typescript
import { 
  RemotionDecryptedText, 
  RemotionTextType, 
  RemotionParticles,
  RemotionCounter 
} from './adapters';
```

## 核心概念

### RemotionAdapterProps

所有适配组件都继承这些基础属性：

```typescript
interface RemotionAdapterProps {
  startFrame?: number;    // 动画开始帧
  duration?: number;      // 动画持续帧数
  delay?: number;         // 延迟帧数
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
}
```

## 文本动画组件

### RemotionDecryptedText

将文本解密效果适配为时间轴动画：

```typescript
<RemotionDecryptedText
  text="Hello World"
  startFrame={30}
  decryptDuration={60}
  characters="ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
  revealDirection="start"
  className="text-2xl font-bold"
/>
```

**属性说明：**
- `text`: 要显示的文本
- `decryptDuration`: 解密动画持续时间（帧）
- `revealDirection`: 解密方向 ('start' | 'end' | 'center')
- `characters`: 用于加密效果的字符集

### RemotionTextType

打字机效果的时间轴版本：

```typescript
<RemotionTextType
  text={["第一行文本", "第二行文本", "第三行文本"]}
  startFrame={0}
  typingSpeed={3}
  pauseDuration={60}
  showCursor={true}
  loop={true}
/>
```

**属性说明：**
- `text`: 字符串或字符串数组
- `typingSpeed`: 打字速度（帧/字符）
- `pauseDuration`: 每行文本间的暂停时间
- `showCursor`: 是否显示光标

### RemotionRotatingText

旋转文本效果：

```typescript
<RemotionRotatingText
  texts={["创新", "设计", "开发", "成功"]}
  startFrame={60}
  rotationInterval={120}
  transitionDuration={30}
  animationType="slide"
/>
```

## 动画效果组件

### RemotionPixelTransition

像素化过渡效果：

```typescript
<RemotionPixelTransition
  startFrame={0}
  activateFrame={30}
  deactivateFrame={150}
  pixelSize={8}
  pattern="random"
  direction="both"
>
  <div>要过渡的内容</div>
</RemotionPixelTransition>
```

### RemotionMagnet

磁性变形效果：

```typescript
<RemotionMagnet
  startFrame={0}
  duration={180}
  strength={0.5}
  pathType="circular"
  autoPath={true}
>
  <div>受磁性影响的内容</div>
</RemotionMagnet>
```

## 背景效果组件

### RemotionParticles

粒子系统背景：

```typescript
<RemotionParticles
  startFrame={0}
  duration={300}
  particleCount={50}
  direction="up"
  speed={1.5}
  particleColors={["#ff6b6b", "#4ecdc4", "#45b7d1"]}
  spawnRate={2}
/>
```

### RemotionBalatro

WebGL着色器背景效果：

```typescript
<RemotionBalatro
  startFrame={0}
  duration={600}
  color1="#DE443B"
  color2="#006BB4"
  color3="#162325"
  animateColors={true}
  animateRotation={true}
/>
```

## UI组件

### RemotionCounter

数字计数动画：

```typescript
<RemotionCounter
  from={0}
  to={1000}
  startFrame={30}
  duration={120}
  decimals={0}
  prefix="$"
  suffix=""
  separator=","
  easing="bounce"
/>
```

### RemotionBounceCards

卡片弹跳动画：

```typescript
<RemotionBounceCards
  cards={[
    { id: "1", title: "卡片1", content: "内容1" },
    { id: "2", title: "卡片2", content: "内容2" }
  ]}
  startFrame={0}
  staggerDelay={15}
  animationType="bounce"
  direction="up"
/>
```

## 高级用法

### 使用适配器Hook

```typescript
import { useRemotionAdapter } from './adapters';

const MyComponent = () => {
  const { progress, interactions, isActive } = useRemotionAdapter({
    startFrame: 30,
    duration: 120,
    simulator: {
      hover: { startFrame: 60, duration: 30 }
    }
  });

  return (
    <div style={{ 
      opacity: progress,
      transform: `scale(${1 + progress * 0.1})`
    }}>
      内容
    </div>
  );
};
```

### 序列动画

```typescript
import { useSequenceAnimation } from './adapters';

const sequences = [
  { startFrame: 0, duration: 30, from: 0, to: 1 },
  { startFrame: 30, duration: 30, from: 1, to: 0.5 },
  { startFrame: 60, duration: 30, from: 0.5, to: 1 }
];

const value = useSequenceAnimation(sequences);
```

## 最佳实践

1. **时间轴规划**: 提前规划好各个组件的时间轴，避免重叠
2. **性能优化**: 对于复杂的WebGL组件，注意帧率影响
3. **响应式设计**: 使用Tailwind CSS确保在不同尺寸下正常显示
4. **调试模式**: 开发环境下某些组件会显示调试信息

## 示例项目

查看 `src/compositions/` 目录下的示例文件，了解如何在实际项目中使用这些组件。
