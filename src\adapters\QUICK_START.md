# 快速开始指南

## 概述

我已经成功将 `src/components/` 目录下的大部分交互组件适配为适合 Remotion 视频制作的版本。这些适配组件移除了鼠标交互、状态管理等依赖，转换为基于时间轴的动画控制。

## 已适配的组件

### 文本动画组件
- ✅ **RemotionDecryptedText** - 文本解密效果
- ✅ **RemotionTextType** - 打字机效果  
- ✅ **RemotionRotatingText** - 旋转文本效果

### 动画效果组件
- ✅ **RemotionPixelTransition** - 像素化过渡效果
- ✅ **RemotionMagnet** - 磁性变形效果

### 背景效果组件
- ✅ **RemotionBalatro** - WebGL着色器背景
- ✅ **RemotionParticles** - 粒子系统背景

### UI组件
- ✅ **RemotionCounter** - 数字计数动画
- ✅ **RemotionBounceCards** - 卡片弹跳动画

## 立即使用

### 1. 导入组件

```typescript
import {
  RemotionDecryptedText,
  RemotionTextType,
  RemotionParticles,
  RemotionCounter
} from './adapters';
```

### 2. 在组合中使用

```typescript
import { AbsoluteFill, Sequence } from 'remotion';

export const MyComposition = () => {
  return (
    <AbsoluteFill>
      {/* 背景粒子 */}
      <RemotionParticles
        startFrame={0}
        duration={300}
        particleCount={50}
        direction="up"
      />
      
      {/* 标题解密效果 */}
      <Sequence from={30} durationInFrames={120}>
        <RemotionDecryptedText
          text="Hello Remotion"
          startFrame={0}
          decryptDuration={90}
          className="text-4xl font-bold"
        />
      </Sequence>
      
      {/* 数字计数 */}
      <Sequence from={150} durationInFrames={90}>
        <RemotionCounter
          from={0}
          to={100}
          startFrame={0}
          duration={60}
          suffix="%"
          className="text-6xl"
        />
      </Sequence>
    </AbsoluteFill>
  );
};
```

### 3. 查看完整示例

运行 `AdaptedComponentsDemo` 组合查看所有适配组件的演示：

```typescript
// 在 src/Root.tsx 中注册
import { AdaptedComponentsDemo } from './compositions/AdaptedComponentsDemo';

// 添加到组合列表
<Composition
  id="AdaptedComponentsDemo"
  component={AdaptedComponentsDemo}
  durationInFrames={900}
  fps={60}
  width={1920}
  height={1080}
/>
```

## 核心特性

### 🎯 时间轴控制
所有组件都支持精确的时间轴控制：
- `startFrame` - 动画开始帧
- `duration` - 动画持续时间
- `delay` - 延迟时间

### 🚀 无交互依赖
移除了所有鼠标事件、状态管理，专为视频渲染优化

### 🎨 保持原始效果
尽可能保持原始组件的视觉效果和动画质量

### 📱 响应式设计
使用 Tailwind CSS 确保在不同分辨率下正常显示

## 扩展更多组件

如需适配更多组件，可以参考现有适配组件的模式：

1. 继承 `RemotionAdapterProps`
2. 使用 `useCurrentFrame()` 和 `interpolate()` 
3. 移除交互事件处理
4. 添加时间轴控制逻辑

## 性能建议

- WebGL组件（如 RemotionBalatro）可能影响渲染性能
- 粒子系统建议控制粒子数量
- 复杂动画建议分段渲染

## 下一步

1. 尝试运行示例组合
2. 根据需要调整组件参数
3. 创建自己的视频组合
4. 如需更多组件适配，可以继续扩展

查看 `src/adapters/README.md` 获取详细的API文档。
