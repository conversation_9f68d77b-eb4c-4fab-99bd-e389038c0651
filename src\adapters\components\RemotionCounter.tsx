import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface RemotionCounterProps extends RemotionAdapterProps {
  from: number;
  to: number;
  duration?: number;
  decimals?: number;
  prefix?: string;
  suffix?: string;
  separator?: string;
  className?: string;
  // Remotion特定属性
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bounce';
  animateOnVisible?: boolean;
  countStartFrame?: number;
}

export const RemotionCounter: React.FC<RemotionCounterProps> = ({
  from,
  to,
  duration = 120, // 2秒 at 60fps
  decimals = 0,
  prefix = '',
  suffix = '',
  separator = ',',
  className = '',
  startFrame = 0,
  delay = 0,
  easing = 'ease-out',
  animateOnVisible = true,
  countStartFrame,
}) => {
  const frame = useCurrentFrame();
  const actualStartFrame = startFrame + delay;
  const actualCountStartFrame = countStartFrame ?? actualStartFrame;

  // 计算当前数值
  const currentValue = useMemo(() => {
    if (!animateOnVisible || frame < actualCountStartFrame) {
      return from;
    }

    const progress = Math.min(
      (frame - actualCountStartFrame) / duration,
      1
    );

    let easedProgress = progress;
    
    // 应用缓动函数
    switch (easing) {
      case 'ease-in':
        easedProgress = progress * progress;
        break;
      case 'ease-out':
        easedProgress = 1 - Math.pow(1 - progress, 2);
        break;
      case 'ease-in-out':
        easedProgress = progress < 0.5 
          ? 2 * progress * progress 
          : 1 - Math.pow(-2 * progress + 2, 2) / 2;
        break;
      case 'bounce':
        if (progress < 1 / 2.75) {
          easedProgress = 7.5625 * progress * progress;
        } else if (progress < 2 / 2.75) {
          easedProgress = 7.5625 * (progress -= 1.5 / 2.75) * progress + 0.75;
        } else if (progress < 2.5 / 2.75) {
          easedProgress = 7.5625 * (progress -= 2.25 / 2.75) * progress + 0.9375;
        } else {
          easedProgress = 7.5625 * (progress -= 2.625 / 2.75) * progress + 0.984375;
        }
        break;
      default: // linear
        easedProgress = progress;
    }

    return interpolate(easedProgress, [0, 1], [from, to]);
  }, [frame, actualCountStartFrame, duration, from, to, easing, animateOnVisible]);

  // 格式化数字
  const formatNumber = (value: number): string => {
    // 处理小数位
    const fixedValue = value.toFixed(decimals);
    
    // 分离整数和小数部分
    const [integerPart, decimalPart] = fixedValue.split('.');
    
    // 添加千位分隔符
    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
    
    // 组合结果
    let result = formattedInteger;
    if (decimals > 0 && decimalPart) {
      result += '.' + decimalPart;
    }
    
    return prefix + result + suffix;
  };

  // 计算透明度动画
  const opacity = interpolate(
    frame,
    [actualStartFrame, actualStartFrame + 10],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // 计算缩放动画（可选的入场效果）
  const scale = interpolate(
    frame,
    [actualStartFrame, actualStartFrame + 20],
    [0.8, 1],
    { 
      extrapolateLeft: 'clamp', 
      extrapolateRight: 'clamp',
      easing: (t) => 1 - Math.pow(1 - t, 3) // ease-out-cubic
    }
  );

  return (
    <span 
      className={`inline-block ${className}`}
      style={{
        opacity,
        transform: `scale(${scale})`,
        transformOrigin: 'center'
      }}
    >
      {formatNumber(currentValue)}
    </span>
  );
};

export default RemotionCounter;
