import React from 'react';
import { AbsoluteFill, Sequence } from 'remotion';
import {
  RemotionDecryptedText,
  RemotionTextType,
  RemotionRotatingText,
  RemotionPixelTransition,
  RemotionMagnet,
  RemotionParticles,
  RemotionBalatro,
  RemotionCounter,
  RemotionBounceCards
} from '../adapters';

export const AdaptedComponentsDemo: React.FC = () => {
  // 示例卡片数据
  const cardData = [
    {
      id: "1",
      title: "创新设计",
      content: "运用最新的设计理念，创造独特的用户体验",
      color: "#ff6b6b"
    },
    {
      id: "2", 
      title: "技术驱动",
      content: "采用前沿技术栈，确保项目的可扩展性和性能",
      color: "#4ecdc4"
    },
    {
      id: "3",
      title: "用户至上",
      content: "以用户需求为核心，打造直观易用的产品界面",
      color: "#45b7d1"
    }
  ];

  return (
    <AbsoluteFill className="bg-black text-white overflow-hidden">
      {/* 背景粒子效果 */}
      <Sequence from={0} durationInFrames={900}>
        <RemotionParticles
          startFrame={0}
          duration={900}
          particleCount={30}
          direction="up"
          speed={0.8}
          particleColors={["#ff6b6b", "#4ecdc4", "#45b7d1", "#feca57"]}
          spawnRate={1}
          className="opacity-30"
        />
      </Sequence>

      {/* WebGL背景效果 */}
      <Sequence from={0} durationInFrames={900}>
        <div className="absolute inset-0 opacity-20">
          <RemotionBalatro
            startFrame={0}
            duration={900}
            color1="#DE443B"
            color2="#006BB4"
            color3="#162325"
            animateColors={true}
            animateRotation={true}
            spinSpeed={2}
          />
        </div>
      </Sequence>

      {/* 第一段：标题解密效果 */}
      <Sequence from={30} durationInFrames={120}>
        <AbsoluteFill className="flex items-center justify-center">
          <RemotionDecryptedText
            text="REMOTION 适配组件库"
            startFrame={0}
            decryptDuration={90}
            revealDirection="center"
            className="text-6xl font-bold text-center"
            encryptedClassName="text-gray-500"
          />
        </AbsoluteFill>
      </Sequence>

      {/* 第二段：副标题打字机效果 */}
      <Sequence from={150} durationInFrames={180}>
        <AbsoluteFill className="flex items-center justify-center pt-20">
          <RemotionTextType
            text="将交互组件转换为时间轴动画"
            startFrame={0}
            typingSpeed={4}
            showCursor={true}
            className="text-2xl text-gray-300 text-center"
          />
        </AbsoluteFill>
      </Sequence>

      {/* 第三段：旋转关键词 */}
      <Sequence from={330} durationInFrames={240}>
        <AbsoluteFill className="flex items-center justify-center">
          <div className="text-center">
            <div className="text-xl text-gray-400 mb-4">核心特性</div>
            <RemotionRotatingText
              texts={["时间轴控制", "无交互依赖", "高性能渲染", "易于使用"]}
              startFrame={0}
              rotationInterval={60}
              transitionDuration={20}
              animationType="slide"
              className="text-4xl font-bold text-blue-400"
            />
          </div>
        </AbsoluteFill>
      </Sequence>

      {/* 第四段：数字计数展示 */}
      <Sequence from={570} durationInFrames={150}>
        <AbsoluteFill className="flex items-center justify-center">
          <div className="grid grid-cols-3 gap-12 text-center">
            <div>
              <RemotionCounter
                from={0}
                to={50}
                startFrame={0}
                duration={90}
                suffix="+"
                easing="bounce"
                className="text-5xl font-bold text-green-400"
              />
              <div className="text-lg text-gray-400 mt-2">适配组件</div>
            </div>
            <div>
              <RemotionCounter
                from={0}
                to={100}
                startFrame={20}
                duration={90}
                suffix="%"
                easing="ease-out"
                className="text-5xl font-bold text-yellow-400"
              />
              <div className="text-lg text-gray-400 mt-2">兼容性</div>
            </div>
            <div>
              <RemotionCounter
                from={0}
                to={60}
                startFrame={40}
                duration={90}
                suffix=" FPS"
                easing="ease-in-out"
                className="text-5xl font-bold text-red-400"
              />
              <div className="text-lg text-gray-400 mt-2">流畅度</div>
            </div>
          </div>
        </AbsoluteFill>
      </Sequence>

      {/* 第五段：卡片展示 */}
      <Sequence from={720} durationInFrames={180}>
        <AbsoluteFill className="flex items-center justify-center p-8">
          <div className="w-full max-w-6xl">
            <div className="text-3xl font-bold text-center mb-8 text-white">
              功能特色
            </div>
            <RemotionBounceCards
              cards={cardData}
              startFrame={0}
              staggerDelay={20}
              animationType="bounce"
              direction="up"
              bounceIntensity={1.2}
              cardClassName="bg-gray-800 border border-gray-700"
            />
          </div>
        </AbsoluteFill>
      </Sequence>

      {/* 磁性效果演示 */}
      <Sequence from={600} durationInFrames={120}>
        <div className="absolute top-1/4 right-8">
          <RemotionMagnet
            startFrame={0}
            duration={120}
            strength={0.3}
            pathType="figure8"
            autoPath={true}
          >
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-bold">
              M
            </div>
          </RemotionMagnet>
        </div>
      </Sequence>

      {/* 像素过渡效果 */}
      <Sequence from={840} durationInFrames={60}>
        <RemotionPixelTransition
          startFrame={0}
          activateFrame={0}
          pixelSize={12}
          pattern="wave"
          direction="out"
          transitionDuration={60}
        >
          <AbsoluteFill className="bg-black" />
        </RemotionPixelTransition>
      </Sequence>
    </AbsoluteFill>
  );
};

export default AdaptedComponentsDemo;
