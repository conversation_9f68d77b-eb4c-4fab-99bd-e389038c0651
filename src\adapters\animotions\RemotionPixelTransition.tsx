import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface RemotionPixelTransitionProps extends RemotionAdapterProps {
  children: React.ReactNode;
  pixelSize?: number;
  transitionDuration?: number;
  staggerDelay?: number;
  direction?: 'in' | 'out' | 'both';
  pattern?: 'random' | 'grid' | 'wave' | 'spiral';
  className?: string;
  // Remotion特定属性
  activateFrame?: number;
  deactivateFrame?: number;
}

export const RemotionPixelTransition: React.FC<RemotionPixelTransitionProps> = ({
  children,
  pixelSize = 10,
  transitionDuration = 60,
  staggerDelay = 2,
  direction = 'both',
  pattern = 'random',
  className = '',
  startFrame = 0,
  duration = 180,
  delay = 0,
  activateFrame,
  deactivateFrame,
}) => {
  const frame = useCurrentFrame();
  const actualStartFrame = startFrame + delay;
  const actualActivateFrame = activateFrame ?? actualStartFrame + 30;
  const actualDeactivateFrame = deactivateFrame ?? actualStartFrame + duration - 60;

  // 计算网格尺寸
  const gridSize = useMemo(() => {
    // 假设容器尺寸，实际应该从props或context获取
    const containerWidth = 400;
    const containerHeight = 300;
    const cols = Math.ceil(containerWidth / pixelSize);
    const rows = Math.ceil(containerHeight / pixelSize);
    return { cols, rows, total: cols * rows };
  }, [pixelSize]);

  // 生成像素动画序列
  const pixelSequence = useMemo(() => {
    const sequence: Array<{ x: number; y: number; delay: number }> = [];
    
    for (let row = 0; row < gridSize.rows; row++) {
      for (let col = 0; col < gridSize.cols; col++) {
        let delay = 0;
        
        switch (pattern) {
          case 'random':
            delay = Math.random() * staggerDelay * 20;
            break;
          case 'grid':
            delay = (row + col) * staggerDelay;
            break;
          case 'wave':
            delay = Math.sin((col / gridSize.cols) * Math.PI * 2) * staggerDelay * 10 + 
                   Math.cos((row / gridSize.rows) * Math.PI * 2) * staggerDelay * 10;
            break;
          case 'spiral':
            const centerX = gridSize.cols / 2;
            const centerY = gridSize.rows / 2;
            const distance = Math.sqrt(Math.pow(col - centerX, 2) + Math.pow(row - centerY, 2));
            delay = distance * staggerDelay;
            break;
        }
        
        sequence.push({
          x: col * pixelSize,
          y: row * pixelSize,
          delay: Math.max(0, delay)
        });
      }
    }
    
    return sequence;
  }, [gridSize, pattern, staggerDelay, pixelSize]);

  // 计算每个像素的可见性
  const getPixelVisibility = (pixelDelay: number, phase: 'in' | 'out') => {
    const phaseStartFrame = phase === 'in' ? actualActivateFrame : actualDeactivateFrame;
    const pixelStartFrame = phaseStartFrame + pixelDelay;
    const pixelEndFrame = pixelStartFrame + transitionDuration;
    
    if (phase === 'in') {
      return interpolate(
        frame,
        [pixelStartFrame, pixelEndFrame],
        [1, 0], // 像素从显示到隐藏，露出内容
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      );
    } else {
      return interpolate(
        frame,
        [pixelStartFrame, pixelEndFrame],
        [0, 1], // 像素从隐藏到显示，遮盖内容
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      );
    }
  };

  // 计算内容可见性
  const contentOpacity = useMemo(() => {
    if (direction === 'in') {
      return frame >= actualActivateFrame ? 1 : 0;
    } else if (direction === 'out') {
      return frame >= actualDeactivateFrame ? 0 : 1;
    } else { // both
      if (frame < actualActivateFrame) return 0;
      if (frame >= actualDeactivateFrame) return 0;
      return 1;
    }
  }, [frame, direction, actualActivateFrame, actualDeactivateFrame]);

  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{
        opacity: interpolate(
          frame,
          [actualStartFrame, actualStartFrame + 10],
          [0, 1],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        )
      }}
    >
      {/* 内容层 */}
      <div 
        style={{ 
          opacity: contentOpacity,
          pointerEvents: 'none'
        }}
      >
        {children}
      </div>
      
      {/* 像素遮罩层 */}
      <div 
        className="absolute inset-0 pointer-events-none"
        style={{ zIndex: 10 }}
      >
        {pixelSequence.map((pixel, index) => {
          let opacity = 0;
          
          if (direction === 'in' || direction === 'both') {
            if (frame >= actualActivateFrame) {
              opacity = Math.max(opacity, getPixelVisibility(pixel.delay, 'in'));
            } else {
              opacity = 1; // 初始状态，像素完全可见
            }
          }
          
          if (direction === 'out' || direction === 'both') {
            if (frame >= actualDeactivateFrame) {
              opacity = Math.max(opacity, getPixelVisibility(pixel.delay, 'out'));
            }
          }
          
          return (
            <div
              key={index}
              className="absolute bg-black"
              style={{
                left: pixel.x,
                top: pixel.y,
                width: pixelSize,
                height: pixelSize,
                opacity,
                transition: 'none'
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export default RemotionPixelTransition;
