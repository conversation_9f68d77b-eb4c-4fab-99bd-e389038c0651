import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface RemotionTextTypeProps extends RemotionAdapterProps {
  text: string | string[];
  typingSpeed?: number;
  pauseDuration?: number;
  deletingSpeed?: number;
  loop?: boolean;
  className?: string;
  showCursor?: boolean;
  cursorCharacter?: string;
  cursorClassName?: string;
  textColors?: string[];
  // Remotion特定属性
  autoStart?: boolean;
  typeStartFrame?: number;
}

export const RemotionTextType: React.FC<RemotionTextTypeProps> = ({
  text,
  typingSpeed = 50,
  pauseDuration = 2000,
  deletingSpeed = 30,
  loop = true,
  className = '',
  showCursor = true,
  cursorCharacter = '|',
  cursorClassName = '',
  textColors = [],
  startFrame = 0,
  duration = 300,
  delay = 0,
  autoStart = true,
  typeStartFrame,
}) => {
  const frame = useCurrentFrame();
  const actualStartFrame = typeStartFrame ?? startFrame + delay;
  
  const texts = Array.isArray(text) ? text : [text];
  
  // 计算当前应该显示的文本和字符
  const { displayedText, currentTextIndex, isDeleting } = useMemo(() => {
    if (!autoStart || frame < actualStartFrame) {
      return { displayedText: '', currentTextIndex: 0, isDeleting: false };
    }

    const framesSinceStart = frame - actualStartFrame;
    
    // 计算每个完整周期的帧数
    const getTextCycleDuration = (textStr: string) => {
      const typingFrames = textStr.length * typingSpeed;
      const pauseFrames = pauseDuration;
      const deletingFrames = textStr.length * deletingSpeed;
      return typingFrames + pauseFrames + deletingFrames;
    };

    // 计算总周期时间
    const totalCycleDuration = texts.reduce((sum, textStr) => sum + getTextCycleDuration(textStr), 0);
    
    if (!loop && framesSinceStart >= totalCycleDuration) {
      // 非循环模式，显示最后一个文本
      return { displayedText: texts[texts.length - 1], currentTextIndex: texts.length - 1, isDeleting: false };
    }

    // 循环模式或在第一个周期内
    const cycleFrame = loop ? framesSinceStart % totalCycleDuration : framesSinceStart;
    
    // 找到当前文本索引和在该文本中的位置
    let accumulatedFrames = 0;
    for (let i = 0; i < texts.length; i++) {
      const textStr = texts[i];
      const textCycleDuration = getTextCycleDuration(textStr);
      
      if (cycleFrame < accumulatedFrames + textCycleDuration) {
        const frameInText = cycleFrame - accumulatedFrames;
        const typingFrames = textStr.length * typingSpeed;
        const pauseFrames = pauseDuration;
        
        if (frameInText < typingFrames) {
          // 正在打字
          const charIndex = Math.floor(frameInText / typingSpeed);
          return {
            displayedText: textStr.substring(0, charIndex + 1),
            currentTextIndex: i,
            isDeleting: false
          };
        } else if (frameInText < typingFrames + pauseFrames) {
          // 暂停状态
          return {
            displayedText: textStr,
            currentTextIndex: i,
            isDeleting: false
          };
        } else {
          // 正在删除
          const deleteFrame = frameInText - typingFrames - pauseFrames;
          const deleteCharIndex = Math.floor(deleteFrame / deletingSpeed);
          const remainingChars = Math.max(0, textStr.length - deleteCharIndex);
          return {
            displayedText: textStr.substring(0, remainingChars),
            currentTextIndex: i,
            isDeleting: true
          };
        }
      }
      
      accumulatedFrames += textCycleDuration;
    }
    
    return { displayedText: '', currentTextIndex: 0, isDeleting: false };
  }, [frame, texts, typingSpeed, pauseDuration, deletingSpeed, loop, actualStartFrame, autoStart]);

  // 光标闪烁动画
  const cursorOpacity = interpolate(
    frame % 30, // 30帧一个周期
    [0, 15, 30],
    [1, 0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  // 获取当前文本颜色
  const currentColor = textColors[currentTextIndex] || '';

  return (
    <span 
      className={className}
      style={{
        color: currentColor,
        opacity: interpolate(
          frame,
          [startFrame + delay, startFrame + delay + 10],
          [0, 1],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        )
      }}
    >
      {displayedText}
      {showCursor && (
        <span
          className={cursorClassName}
          style={{
            opacity: cursorOpacity,
            marginLeft: '1px'
          }}
        >
          {cursorCharacter}
        </span>
      )}
    </span>
  );
};

export default RemotionTextType;
