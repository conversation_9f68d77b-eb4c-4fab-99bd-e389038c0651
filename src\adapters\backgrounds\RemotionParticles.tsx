import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: string;
  opacity: number;
  life: number;
  maxLife: number;
}

interface RemotionParticlesProps extends RemotionAdapterProps {
  particleCount?: number;
  particleSize?: number;
  particleColor?: string;
  particleColors?: string[];
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'random';
  fadeIn?: boolean;
  fadeOut?: boolean;
  className?: string;
  // Remotion特定属性
  spawnRate?: number;
  maxParticles?: number;
  gravity?: number;
  wind?: number;
}

export const RemotionParticles: React.FC<RemotionParticlesProps> = ({
  particleCount = 50,
  particleSize = 4,
  particleColor = '#ffffff',
  particleColors = [],
  speed = 1,
  direction = 'up',
  fadeIn = true,
  fadeOut = true,
  className = '',
  startFrame = 0,
  duration = 300,
  delay = 0,
  spawnRate = 2,
  maxParticles = 100,
  gravity = 0.01,
  wind = 0,
}) => {
  const frame = useCurrentFrame();
  const actualStartFrame = startFrame + delay;

  // 生成粒子系统
  const particles = useMemo(() => {
    if (frame < actualStartFrame) return [];

    const framesSinceStart = frame - actualStartFrame;
    const particleList: Particle[] = [];
    
    // 计算应该生成的粒子总数
    const totalParticlesToSpawn = Math.min(
      Math.floor(framesSinceStart * spawnRate),
      maxParticles
    );

    for (let i = 0; i < totalParticlesToSpawn; i++) {
      // 计算粒子的生成时间
      const spawnFrame = Math.floor(i / spawnRate);
      const particleAge = framesSinceStart - spawnFrame;
      
      if (particleAge < 0) continue;

      // 初始位置
      let startX = Math.random();
      let startY = Math.random();
      
      // 根据方向调整初始位置
      switch (direction) {
        case 'up':
          startY = 1.2; // 从底部开始
          break;
        case 'down':
          startY = -0.2; // 从顶部开始
          break;
        case 'left':
          startX = 1.2; // 从右侧开始
          break;
        case 'right':
          startX = -0.2; // 从左侧开始
          break;
      }

      // 初始速度
      let vx = 0;
      let vy = 0;
      
      switch (direction) {
        case 'up':
          vy = -speed * (0.5 + Math.random() * 0.5);
          vx = (Math.random() - 0.5) * speed * 0.3;
          break;
        case 'down':
          vy = speed * (0.5 + Math.random() * 0.5);
          vx = (Math.random() - 0.5) * speed * 0.3;
          break;
        case 'left':
          vx = -speed * (0.5 + Math.random() * 0.5);
          vy = (Math.random() - 0.5) * speed * 0.3;
          break;
        case 'right':
          vx = speed * (0.5 + Math.random() * 0.5);
          vy = (Math.random() - 0.5) * speed * 0.3;
          break;
        case 'random':
          vx = (Math.random() - 0.5) * speed * 2;
          vy = (Math.random() - 0.5) * speed * 2;
          break;
      }

      // 计算当前位置（考虑物理效果）
      let currentX = startX + vx * particleAge * 0.01;
      let currentY = startY + vy * particleAge * 0.01;
      
      // 应用重力
      currentY += gravity * particleAge * particleAge * 0.0001;
      
      // 应用风力
      currentX += wind * particleAge * 0.001;

      // 粒子生命周期
      const maxLife = 120 + Math.random() * 60; // 2-3秒
      const life = Math.min(particleAge, maxLife);
      
      // 如果粒子超出边界或生命结束，跳过
      if (currentX < -0.1 || currentX > 1.1 || currentY < -0.1 || currentY > 1.1 || life >= maxLife) {
        continue;
      }

      // 粒子属性
      const size = particleSize * (0.5 + Math.random() * 0.5);
      const color = particleColors.length > 0 
        ? particleColors[i % particleColors.length]
        : particleColor;

      // 计算透明度
      let opacity = 1;
      if (fadeIn && life < 30) {
        opacity = life / 30;
      }
      if (fadeOut && life > maxLife - 30) {
        opacity = (maxLife - life) / 30;
      }

      particleList.push({
        id: i,
        x: currentX,
        y: currentY,
        vx,
        vy,
        size,
        color,
        opacity,
        life,
        maxLife
      });
    }

    return particleList;
  }, [frame, actualStartFrame, spawnRate, maxParticles, direction, speed, gravity, wind, particleSize, particleColor, particleColors, fadeIn, fadeOut]);

  // 计算整体透明度
  const containerOpacity = interpolate(
    frame,
    [actualStartFrame, actualStartFrame + 10],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div 
      className={`absolute inset-0 pointer-events-none ${className}`}
      style={{ opacity: containerOpacity }}
    >
      {particles.map((particle) => (
        <div
          key={particle.id}
          className="absolute rounded-full"
          style={{
            left: `${particle.x * 100}%`,
            top: `${particle.y * 100}%`,
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            backgroundColor: particle.color,
            opacity: particle.opacity,
            transform: 'translate(-50%, -50%)',
            boxShadow: `0 0 ${particle.size * 2}px ${particle.color}`,
            transition: 'none'
          }}
        />
      ))}
    </div>
  );
};

export default RemotionParticles;
