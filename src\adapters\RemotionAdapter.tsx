import React from 'react';
import { useCurrentFrame, useVideoConfig, interpolate } from 'remotion';

// 基础适配器接口
export interface RemotionAdapterProps {
  startFrame?: number;
  duration?: number;
  delay?: number;
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out';
}

// 交互状态模拟器
export interface InteractionSimulator {
  hover?: {
    startFrame: number;
    duration: number;
    intensity?: number;
  };
  click?: {
    frame: number;
    duration?: number;
  };
  scroll?: {
    startFrame: number;
    endFrame: number;
    distance: number;
  };
  focus?: {
    startFrame: number;
    duration: number;
  };
}

// 通用适配器Hook
export const useRemotionAdapter = (
  props: RemotionAdapterProps & { simulator?: InteractionSimulator }
) => {
  const frame = useCurrentFrame();
  const { fps } = useVideoConfig();
  
  const {
    startFrame = 0,
    duration = 60,
    delay = 0,
    easing = 'ease-out',
    simulator
  } = props;

  const actualStartFrame = startFrame + delay;
  const endFrame = actualStartFrame + duration;

  // 基础进度计算
  const progress = interpolate(
    frame,
    [actualStartFrame, endFrame],
    [0, 1],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: getEasingFunction(easing)
    }
  );

  // 交互状态模拟
  const interactions = {
    isHovered: false,
    isClicked: false,
    scrollProgress: 0,
    isFocused: false,
    mousePosition: { x: 0.5, y: 0.5 }
  };

  if (simulator?.hover) {
    const hoverStart = simulator.hover.startFrame;
    const hoverEnd = hoverStart + simulator.hover.duration;
    interactions.isHovered = frame >= hoverStart && frame <= hoverEnd;
  }

  if (simulator?.click) {
    const clickFrame = simulator.click.frame;
    const clickDuration = simulator.click.duration || 10;
    interactions.isClicked = frame >= clickFrame && frame <= clickFrame + clickDuration;
  }

  if (simulator?.scroll) {
    const scrollProgress = interpolate(
      frame,
      [simulator.scroll.startFrame, simulator.scroll.endFrame],
      [0, simulator.scroll.distance],
      { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
    );
    interactions.scrollProgress = scrollProgress;
  }

  if (simulator?.focus) {
    const focusStart = simulator.focus.startFrame;
    const focusEnd = focusStart + simulator.focus.duration;
    interactions.isFocused = frame >= focusStart && frame <= focusEnd;
  }

  // 模拟鼠标位置（可以基于时间轴创建路径）
  const mouseX = interpolate(
    frame,
    [actualStartFrame, endFrame],
    [0.2, 0.8],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );
  const mouseY = 0.5 + Math.sin(frame * 0.1) * 0.2;
  interactions.mousePosition = { x: mouseX, y: mouseY };

  return {
    frame,
    progress,
    interactions,
    isActive: frame >= actualStartFrame && frame <= endFrame,
    fps
  };
};

// 缓动函数映射
function getEasingFunction(easing: string) {
  switch (easing) {
    case 'ease-in':
      return (t: number) => t * t;
    case 'ease-out':
      return (t: number) => 1 - Math.pow(1 - t, 2);
    case 'ease-in-out':
      return (t: number) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
    default:
      return undefined; // linear
  }
}

// 高阶组件：将交互组件转换为Remotion组件
export function withRemotionAdapter<T extends object>(
  Component: React.ComponentType<T>,
  defaultSimulator?: InteractionSimulator
) {
  return React.forwardRef<
    any,
    T & RemotionAdapterProps & { simulator?: InteractionSimulator }
  >((props, ref) => {
    const { startFrame, duration, delay, easing, simulator, ...componentProps } = props;
    
    const adapterData = useRemotionAdapter({
      startFrame,
      duration,
      delay,
      easing,
      simulator: simulator || defaultSimulator
    });

    // 将适配器数据注入到组件props中
    const enhancedProps = {
      ...componentProps,
      remotionAdapter: adapterData,
      // 模拟交互事件
      onMouseEnter: () => {}, // 空函数，防止报错
      onMouseLeave: () => {},
      onClick: () => {},
      onFocus: () => {},
      onBlur: () => {},
    } as T;

    return <Component ref={ref} {...enhancedProps} />;
  });
}

// 时间轴控制的动画值生成器
export const useAnimatedValue = (
  config: {
    startFrame: number;
    endFrame: number;
    from: number;
    to: number;
    easing?: string;
  }
) => {
  const frame = useCurrentFrame();
  
  return interpolate(
    frame,
    [config.startFrame, config.endFrame],
    [config.from, config.to],
    {
      extrapolateLeft: 'clamp',
      extrapolateRight: 'clamp',
      easing: getEasingFunction(config.easing || 'ease-out')
    }
  );
};

// 序列动画辅助函数
export const useSequenceAnimation = (
  sequences: Array<{
    startFrame: number;
    duration: number;
    from: number;
    to: number;
    easing?: string;
  }>
) => {
  const frame = useCurrentFrame();
  
  for (const sequence of sequences) {
    const endFrame = sequence.startFrame + sequence.duration;
    if (frame >= sequence.startFrame && frame <= endFrame) {
      return interpolate(
        frame,
        [sequence.startFrame, endFrame],
        [sequence.from, sequence.to],
        {
          extrapolateLeft: 'clamp',
          extrapolateRight: 'clamp',
          easing: getEasingFunction(sequence.easing || 'ease-out')
        }
      );
    }
  }
  
  // 返回最后一个序列的结束值
  return sequences[sequences.length - 1]?.to || 0;
};
