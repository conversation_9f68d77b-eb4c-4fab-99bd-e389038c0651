import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface CardData {
  id: string;
  title: string;
  content: string;
  image?: string;
  color?: string;
}

interface RemotionBounceCardsProps extends RemotionAdapterProps {
  cards: CardData[];
  staggerDelay?: number;
  bounceIntensity?: number;
  className?: string;
  cardClassName?: string;
  // Remotion特定属性
  animationType?: 'bounce' | 'slide' | 'fade' | 'scale';
  direction?: 'up' | 'down' | 'left' | 'right';
  showSequentially?: boolean;
  cardDuration?: number;
}

export const RemotionBounceCards: React.FC<RemotionBounceCardsProps> = ({
  cards,
  staggerDelay = 10,
  bounceIntensity = 1,
  className = '',
  cardClassName = '',
  startFrame = 0,
  duration = 180,
  delay = 0,
  animationType = 'bounce',
  direction = 'up',
  showSequentially = true,
  cardDuration = 60,
}) => {
  const frame = useCurrentFrame();
  const actualStartFrame = startFrame + delay;

  // 计算每张卡片的动画状态
  const cardAnimations = useMemo(() => {
    return cards.map((card, index) => {
      const cardStartFrame = showSequentially 
        ? actualStartFrame + index * staggerDelay
        : actualStartFrame;
      
      const cardEndFrame = cardStartFrame + cardDuration;
      
      // 基础进度
      const progress = interpolate(
        frame,
        [cardStartFrame, cardEndFrame],
        [0, 1],
        { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
      );

      // 计算不同动画类型的变换
      let transform = '';
      let opacity = 1;

      if (frame < cardStartFrame) {
        opacity = 0;
      } else {
        switch (animationType) {
          case 'bounce':
            const bounceProgress = progress;
            let bounceY = 0;
            
            if (bounceProgress < 1) {
              // 弹跳效果计算
              const bounceValue = Math.sin(bounceProgress * Math.PI * 3) * 
                               Math.pow(1 - bounceProgress, 2) * 
                               bounceIntensity * 20;
              
              bounceY = direction === 'up' ? -bounceValue : 
                       direction === 'down' ? bounceValue : 0;
              
              const bounceX = direction === 'left' ? -bounceValue :
                             direction === 'right' ? bounceValue : 0;
              
              transform = `translate(${bounceX}px, ${bounceY}px)`;
            }
            
            // 初始位移
            const initialOffset = interpolate(
              progress,
              [0, 0.6],
              [direction === 'up' ? 50 : direction === 'down' ? -50 : 
               direction === 'left' ? 50 : direction === 'right' ? -50 : 0, 0],
              { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
            );
            
            const initialY = direction === 'up' || direction === 'down' ? initialOffset : 0;
            const initialX = direction === 'left' || direction === 'right' ? initialOffset : 0;
            
            transform = `translate(${initialX}px, ${initialY + bounceY}px)`;
            break;

          case 'slide':
            const slideOffset = interpolate(
              progress,
              [0, 1],
              [direction === 'up' ? 100 : direction === 'down' ? -100 :
               direction === 'left' ? 100 : direction === 'right' ? -100 : 0, 0],
              { 
                extrapolateLeft: 'clamp', 
                extrapolateRight: 'clamp',
                easing: (t) => 1 - Math.pow(1 - t, 3) // ease-out-cubic
              }
            );
            
            const slideY = direction === 'up' || direction === 'down' ? slideOffset : 0;
            const slideX = direction === 'left' || direction === 'right' ? slideOffset : 0;
            
            transform = `translate(${slideX}px, ${slideY}px)`;
            break;

          case 'fade':
            opacity = interpolate(
              progress,
              [0, 1],
              [0, 1],
              { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
            );
            break;

          case 'scale':
            const scale = interpolate(
              progress,
              [0, 1],
              [0.3, 1],
              { 
                extrapolateLeft: 'clamp', 
                extrapolateRight: 'clamp',
                easing: (t) => {
                  // 弹性缩放效果
                  const c1 = 1.70158;
                  const c3 = c1 + 1;
                  return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
                }
              }
            );
            
            transform = `scale(${scale})`;
            opacity = interpolate(
              progress,
              [0, 0.3],
              [0, 1],
              { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
            );
            break;
        }
      }

      return {
        transform,
        opacity,
        progress,
        isVisible: frame >= cardStartFrame
      };
    });
  }, [frame, cards.length, actualStartFrame, staggerDelay, cardDuration, showSequentially, animationType, direction, bounceIntensity]);

  // 容器透明度
  const containerOpacity = interpolate(
    frame,
    [actualStartFrame, actualStartFrame + 10],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div 
      className={`grid gap-4 ${className}`}
      style={{ 
        opacity: containerOpacity,
        gridTemplateColumns: `repeat(auto-fit, minmax(250px, 1fr))`
      }}
    >
      {cards.map((card, index) => {
        const animation = cardAnimations[index];
        
        return (
          <div
            key={card.id}
            className={`bg-white rounded-lg shadow-lg overflow-hidden ${cardClassName}`}
            style={{
              transform: animation.transform,
              opacity: animation.opacity,
              transformOrigin: 'center',
              transition: 'none'
            }}
          >
            {card.image && (
              <div 
                className="h-48 bg-cover bg-center"
                style={{ 
                  backgroundImage: `url(${card.image})`,
                  backgroundColor: card.color || '#f3f4f6'
                }}
              />
            )}
            
            <div className="p-6">
              <h3 className="text-xl font-bold mb-2 text-gray-800">
                {card.title}
              </h3>
              <p className="text-gray-600">
                {card.content}
              </p>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default RemotionBounceCards;
