import React, { useMemo } from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { RemotionAdapterProps } from '../RemotionAdapter';

interface RemotionMagnetProps extends RemotionAdapterProps {
  children: React.ReactNode;
  strength?: number;
  range?: number;
  className?: string;
  // Remotion特定属性
  magnetPath?: Array<{ x: number; y: number; frame: number }>;
  autoPath?: boolean;
  pathType?: 'linear' | 'circular' | 'figure8' | 'random';
}

export const RemotionMagnet: React.FC<RemotionMagnetProps> = ({
  children,
  strength = 0.3,
  range = 100,
  className = '',
  startFrame = 0,
  duration = 300,
  delay = 0,
  magnetPath,
  autoPath = true,
  pathType = 'circular',
}) => {
  const frame = useCurrentFrame();
  const actualStartFrame = startFrame + delay;

  // 生成自动路径
  const generatedPath = useMemo(() => {
    if (magnetPath) return magnetPath;
    if (!autoPath) return [];

    const pathPoints: Array<{ x: number; y: number; frame: number }> = [];
    const totalFrames = duration;
    const stepSize = 5; // 每5帧一个点

    for (let f = 0; f <= totalFrames; f += stepSize) {
      let x = 0.5; // 默认中心
      let y = 0.5;

      switch (pathType) {
        case 'linear':
          x = interpolate(f, [0, totalFrames], [0.2, 0.8]);
          y = 0.5;
          break;
        case 'circular':
          const angle = (f / totalFrames) * Math.PI * 2;
          x = 0.5 + Math.cos(angle) * 0.3;
          y = 0.5 + Math.sin(angle) * 0.3;
          break;
        case 'figure8':
          const t = (f / totalFrames) * Math.PI * 4;
          x = 0.5 + Math.sin(t) * 0.3;
          y = 0.5 + Math.sin(t * 2) * 0.2;
          break;
        case 'random':
          // 使用伪随机生成平滑路径
          const seed1 = Math.sin(f * 0.02) * Math.cos(f * 0.015);
          const seed2 = Math.cos(f * 0.018) * Math.sin(f * 0.022);
          x = 0.5 + seed1 * 0.3;
          y = 0.5 + seed2 * 0.3;
          break;
      }

      pathPoints.push({ x, y, frame: f });
    }

    return pathPoints;
  }, [magnetPath, autoPath, pathType, duration]);

  // 计算当前磁铁位置
  const currentMagnetPosition = useMemo(() => {
    if (generatedPath.length === 0) return { x: 0.5, y: 0.5 };

    const framesSinceStart = frame - actualStartFrame;
    
    if (framesSinceStart < 0) return generatedPath[0];
    if (framesSinceStart >= duration) return generatedPath[generatedPath.length - 1];

    // 在路径点之间插值
    for (let i = 0; i < generatedPath.length - 1; i++) {
      const current = generatedPath[i];
      const next = generatedPath[i + 1];
      
      if (framesSinceStart >= current.frame && framesSinceStart <= next.frame) {
        const progress = (framesSinceStart - current.frame) / (next.frame - current.frame);
        return {
          x: interpolate(progress, [0, 1], [current.x, next.x]),
          y: interpolate(progress, [0, 1], [current.y, next.y])
        };
      }
    }

    return generatedPath[generatedPath.length - 1];
  }, [frame, actualStartFrame, duration, generatedPath]);

  // 计算磁性变形效果
  const magnetEffect = useMemo(() => {
    if (frame < actualStartFrame) return { transform: '', filter: '' };

    const magnetX = currentMagnetPosition.x;
    const magnetY = currentMagnetPosition.y;

    // 计算距离磁铁的相对位置（假设元素在容器中心）
    const elementX = 0.5;
    const elementY = 0.5;
    
    const deltaX = magnetX - elementX;
    const deltaY = magnetY - elementY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    // 计算磁性强度（距离越近，效果越强）
    const normalizedDistance = Math.min(distance / (range / 100), 1);
    const magnetStrength = (1 - normalizedDistance) * strength;
    
    // 计算变形参数
    const translateX = deltaX * magnetStrength * 50; // 像素偏移
    const translateY = deltaY * magnetStrength * 50;
    const scaleX = 1 + magnetStrength * 0.1;
    const scaleY = 1 + magnetStrength * 0.1;
    const skewX = deltaX * magnetStrength * 5; // 倾斜角度
    const skewY = deltaY * magnetStrength * 5;

    const transform = `
      translate(${translateX}px, ${translateY}px)
      scale(${scaleX}, ${scaleY})
      skew(${skewX}deg, ${skewY}deg)
    `;

    // 添加模糊效果模拟磁场扭曲
    const blurAmount = magnetStrength * 2;
    const filter = `blur(${blurAmount}px)`;

    return { transform, filter };
  }, [frame, actualStartFrame, currentMagnetPosition, strength, range]);

  // 计算整体透明度
  const opacity = interpolate(
    frame,
    [actualStartFrame, actualStartFrame + 10],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );

  return (
    <div className={`relative ${className}`} style={{ opacity }}>
      {/* 磁铁可视化（可选，用于调试） */}
      {process.env.NODE_ENV === 'development' && (
        <div
          className="absolute w-4 h-4 bg-red-500 rounded-full pointer-events-none z-50"
          style={{
            left: `${currentMagnetPosition.x * 100}%`,
            top: `${currentMagnetPosition.y * 100}%`,
            transform: 'translate(-50%, -50%)',
            opacity: frame >= actualStartFrame ? 0.7 : 0
          }}
        />
      )}
      
      {/* 受磁性影响的内容 */}
      <div
        style={{
          transform: magnetEffect.transform,
          filter: magnetEffect.filter,
          transformOrigin: 'center',
          transition: 'none'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default RemotionMagnet;
