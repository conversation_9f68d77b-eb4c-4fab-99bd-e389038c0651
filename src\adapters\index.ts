// Remotion适配器系统
export { 
  useRemotionAdapter, 
  withRemotionAdapter, 
  useAnimatedValue, 
  useSequenceAnimation 
} from './RemotionAdapter';
export type { 
  RemotionAdapterProps, 
  InteractionSimulator 
} from './RemotionAdapter';

// 文本动画组件
export { default as RemotionDecryptedText } from './textanimations/RemotionDecryptedText';
export { default as RemotionTextType } from './textanimations/RemotionTextType';
export { default as RemotionRotatingText } from './textanimations/RemotionRotatingText';

// 动画效果组件
export { default as RemotionPixelTransition } from './animotions/RemotionPixelTransition';
export { default as RemotionMagnet } from './animotions/RemotionMagnet';

// 背景效果组件
export { default as RemotionBalatro } from './backgrounds/RemotionBalatro';
export { default as RemotionParticles } from './backgrounds/RemotionParticles';

// UI组件
export { default as RemotionCounter } from './components/RemotionCounter';
export { default as RemotionBounceCards } from './components/RemotionBounceCards';

// 重新导出原始组件（已经适配Remotion的）
export { AnimatedText } from '../components/AnimatedText';
export { DataChart } from '../components/DataChart';
